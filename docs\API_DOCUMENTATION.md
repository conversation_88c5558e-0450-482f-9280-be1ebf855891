# Quranic Insights API Documentation

## Overview

The Quranic Insights API provides a comprehensive set of endpoints for managing user data, AI insights, and Quranic content. The API supports both authenticated and anonymous users.

## Base URL

```
Production: https://akhtiyivutfqwsjiltno.supabase.co
```

## Authentication

### Headers

```
Authorization: Bearer <access_token>
apikey: <anon_key>
X-Anonymous-ID: <anonymous_id> (for anonymous users)
```

### Anonymous Users

Anonymous users can access most features by including an `X-Anonymous-ID` header. This ID should be generated client-side and stored locally.

## Endpoints

### Authentication

#### Sign Up
```http
POST /auth/v1/signup
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securepassword",
  "data": {
    "display_name": "User Name"
  }
}
```

#### Sign In
```http
POST /auth/v1/token?grant_type=password
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securepassword"
}
```

#### Convert Anonymous to Authenticated
```http
POST /rest/v1/rpc/link_anonymous_user
Content-Type: application/json
Authorization: Bearer <access_token>

{
  "anonymous_id": "anon_123456"
}
```

### User Profile

#### Get Profile
```http
GET /rest/v1/user_profiles?id=eq.<user_id>
Authorization: Bearer <access_token>
```

#### Update Profile
```http
PATCH /rest/v1/user_profiles?id=eq.<user_id>
Content-Type: application/json
Authorization: Bearer <access_token>

{
  "display_name": "New Name",
  "avatar_url": "https://example.com/avatar.jpg"
}
```

### User Preferences

#### Get Preferences
```http
GET /rest/v1/user_preferences?id=eq.<user_id>
Authorization: Bearer <access_token>
```

#### Update Preferences
```http
PUT /rest/v1/user_preferences
Content-Type: application/json
Authorization: Bearer <access_token>
Prefer: return=representation

{
  "id": "<user_id>",
  "theme_mode": "dark",
  "font_size": 18,
  "reading_mode": "mushaf",
  "show_translation": true,
  "translation_language": "en"
}
```

### Bookmarks

#### List Bookmarks
```http
GET /rest/v1/user_bookmarks?user_id=eq.<user_id>&order=created_at.desc
Authorization: Bearer <access_token>
```

#### Create Bookmark
```http
POST /rest/v1/user_bookmarks
Content-Type: application/json
Authorization: Bearer <access_token>
Prefer: return=representation

{
  "user_id": "<user_id>",
  "verse_key": "2:255",
  "surah_number": 2,
  "verse_number": 255,
  "surah_name": "Al-Baqarah",
  "verse_text": "Ayatul Kursi...",
  "folder": "favorites",
  "tags": ["ayatul-kursi", "protection"],
  "note": "Beautiful verse about Allah's throne"
}
```

#### Update Bookmark
```http
PATCH /rest/v1/user_bookmarks?id=eq.<bookmark_id>
Content-Type: application/json
Authorization: Bearer <access_token>

{
  "note": "Updated note",
  "tags": ["ayatul-kursi", "protection", "memorization"]
}
```

#### Delete Bookmark
```http
DELETE /rest/v1/user_bookmarks?id=eq.<bookmark_id>
Authorization: Bearer <access_token>
```

### AI Insights

#### Get Cached Insights
```http
GET /rest/v1/ai_cache?verse_key=eq.2:255&insight_type=eq.tafsir
Authorization: Bearer <access_token>
```

#### Generate New Insight
```http
POST /rest/v1/rpc/generate_ai_insight
Content-Type: application/json
Authorization: Bearer <access_token>

{
  "verse_key": "2:255",
  "insight_type": "reflection",
  "language": "en",
  "user_context": "seeking comfort"
}
```

### Scholar Reviews

#### Submit Content for Review
```http
POST /rest/v1/scholar_reviews
Content-Type: application/json
Authorization: Bearer <access_token>

{
  "content_type": "ai_insight",
  "content_id": "<insight_id>",
  "content_text": "AI generated tafsir...",
  "review_type": "accuracy"
}
```

#### Get Review Status
```http
GET /rest/v1/scholar_reviews?content_id=eq.<content_id>
Authorization: Bearer <access_token>
```

### Quran Corrections

#### Get Approved Corrections
```http
GET /rest/v1/quran_corrections?status=eq.approved&order=priority.desc
Authorization: Bearer <access_token>
```

#### Check Sync Status
```http
GET /rest/v1/correction_sync_status?user_id=eq.<user_id>
Authorization: Bearer <access_token>
```

### Khatmah (Reading Plans)

#### List Khatmahs
```http
GET /rest/v1/user_khatmahs?user_id=eq.<user_id>&is_completed=eq.false
Authorization: Bearer <access_token>
```

#### Create Khatmah
```http
POST /rest/v1/user_khatmahs
Content-Type: application/json
Authorization: Bearer <access_token>

{
  "user_id": "<user_id>",
  "name": "Ramadan Reading",
  "current_page": 1,
  "start_page": 1,
  "end_page": 604,
  "days_count": 30,
  "color": 16711680
}
```

#### Update Progress
```http
PATCH /rest/v1/user_khatmahs?id=eq.<khatmah_id>
Content-Type: application/json
Authorization: Bearer <access_token>

{
  "current_page": 125
}
```

## Real-time Subscriptions

### Subscribe to Bookmarks
```javascript
const channel = supabase
  .channel('bookmarks-changes')
  .on(
    'postgres_changes',
    {
      event: '*',
      schema: 'public',
      table: 'user_bookmarks',
      filter: `user_id=eq.${userId}`
    },
    (payload) => {
      console.log('Bookmark changed:', payload)
    }
  )
  .subscribe()
```

### Subscribe to Quran Corrections
```javascript
const channel = supabase
  .channel('quran-corrections')
  .on(
    'postgres_changes',
    {
      event: 'INSERT',
      schema: 'public',
      table: 'quran_corrections',
      filter: 'status=eq.approved'
    },
    (payload) => {
      console.log('New correction approved:', payload)
    }
  )
  .subscribe()
```

## Rate Limiting

| User Type | Requests per Hour | Notes |
|-----------|------------------|--------|
| Authenticated | 100 | Standard limit |
| Anonymous | 50 | Half of authenticated |
| IP-based | 25 | When no auth/anonymous ID |

### Rate Limit Headers
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

## Error Responses

### 400 Bad Request
```json
{
  "error": "Invalid request",
  "message": "Missing required field: verse_key"
}
```

### 401 Unauthorized
```json
{
  "error": "Unauthorized",
  "message": "Invalid or expired token"
}
```

### 429 Too Many Requests
```json
{
  "error": "Rate limit exceeded",
  "message": "Too many requests. Please try again in 3600 seconds."
}
```

### 500 Internal Server Error
```json
{
  "error": "Internal server error",
  "message": "An unexpected error occurred"
}
```

## Webhooks

### Available Webhooks

1. **User Registration**
   - Triggered when a new user signs up
   - Payload includes user profile data

2. **Anonymous Conversion**
   - Triggered when anonymous user converts to authenticated
   - Payload includes migrated data summary

3. **Scholar Review Completed**
   - Triggered when a scholar completes a review
   - Payload includes review result

### Webhook Security
All webhooks include a signature header for verification:
```
X-Webhook-Signature: sha256=<signature>
```

## SDK Examples

### Flutter/Dart
```dart
final supabase = Supabase.instance.client;

// Anonymous user
final bookmarks = await supabase
  .from('user_bookmarks')
  .select()
  .eq('user_id', anonymousId);

// Authenticated user
final profile = await supabase
  .from('user_profiles')
  .select()
  .eq('id', supabase.auth.currentUser!.id)
  .single();
```

### JavaScript
```javascript
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(url, anonKey)

// Get bookmarks
const { data, error } = await supabase
  .from('user_bookmarks')
  .select('*')
  .eq('user_id', userId)
  .order('created_at', { ascending: false })
```

## Best Practices

1. **Always use RLS**: Never disable Row Level Security
2. **Batch requests**: Combine multiple operations when possible
3. **Cache responses**: Especially for Quran data and AI insights
4. **Handle offline**: Implement proper offline support
5. **Respect rate limits**: Implement exponential backoff
6. **Secure anonymous IDs**: Generate cryptographically secure IDs

## Support

- Documentation: https://docs.quranicinsights.app
- Status Page: https://status.quranicinsights.app
- Support Email: <EMAIL>