{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987010b81292a2d50d3fb8d0efd422df47", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9876b03fbb551675bf451a4f81da1ac7c2", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98060f2b4cbfcbd4975fd07e90f17277e5", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a371ead5cb62d712b6d33a7794dedd6f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98060f2b4cbfcbd4975fd07e90f17277e5", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a71390f32eca95b3b91b58fb7463c295", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989f712c4d2e996f79987a079a7693ce9f", "guid": "bfdfe7dc352907fc980b868725387e98193cbc37df47ca6bd1f7b2987d581868", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804f9ace45079a4facb6056b6f688a010", "guid": "bfdfe7dc352907fc980b868725387e981c77104c961383e47ab5bc0ee51362be", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dae6798eb789c8cfb92b77debb01c45a", "guid": "bfdfe7dc352907fc980b868725387e983b9e66575394fd880d61297cab95ad0a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fcbe82fd0a121563668121ba845d76f", "guid": "bfdfe7dc352907fc980b868725387e98bcf4c9beab448fb2d7149cd3b63963a4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98419cb89a0ec223a61c0430733c6c17f4", "guid": "bfdfe7dc352907fc980b868725387e987c0a191d08bda3e5833bf846878d0b72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a493045968c800b79507b482dc6d7a53", "guid": "bfdfe7dc352907fc980b868725387e986c2a7ffc1545121fc8cd04a3d7b29d5a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb40792f741eee43ee6f7d0a5a0b751d", "guid": "bfdfe7dc352907fc980b868725387e98ebc431f31b4ba40239ec86964236fe84", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4958659658d9883e3044739d783290e", "guid": "bfdfe7dc352907fc980b868725387e987a065af2610f73e182d8aef95321d80e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cc01b0283a3a7551c68d96a283f5862", "guid": "bfdfe7dc352907fc980b868725387e98df3885b7b8c513219172a52557fe731e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981dba5177fa583ad1361ed8f7a0e5e778", "guid": "bfdfe7dc352907fc980b868725387e9854e6ca851f63ed3cc2e8e08edc65479a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b34ce867e20c3293540546a816882b7e", "guid": "bfdfe7dc352907fc980b868725387e98a45a633473bf72558a786ad97ba60a42", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce1c4c7e33adc61aa4b4fed199bcacb3", "guid": "bfdfe7dc352907fc980b868725387e986e7fdef0912afe567091baba0ec28fcc", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b1c6e0dac1c0f3288cdfd038dfd59299", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982a6ef9a2846c09e80289747794ea908b", "guid": "bfdfe7dc352907fc980b868725387e98865d81ee9ba06a26516e000cf4f66bc2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a56a270c56db6952dd41e5bc4bdcc74", "guid": "bfdfe7dc352907fc980b868725387e9865cd37c44f872c70257508efae941891"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e63e93b637bb10368d0625520637181d", "guid": "bfdfe7dc352907fc980b868725387e98b62591ad28ed7a89009145d6d76a21c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db1502a37f7639ecaf04822de68b3f67", "guid": "bfdfe7dc352907fc980b868725387e98f0dfb7ca0ca36190fab8c20d10f6e2f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858da65142518584f0b45f5e07a183308", "guid": "bfdfe7dc352907fc980b868725387e981cee213e907a8cfbe7a4bca267a59968"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f41528419a3fd4a626ca33f3628abbb9", "guid": "bfdfe7dc352907fc980b868725387e98443745df8a468cbf6c91df114c6b4892"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870e0bae0ade2a7611e5406c9e02e8885", "guid": "bfdfe7dc352907fc980b868725387e98381aacafdc460bf824e5a270b95c9bde"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98031dc0202a90a477b61034bd7faf42a9", "guid": "bfdfe7dc352907fc980b868725387e98b48d635eb12a83e92dded002cc742c7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c4f2a486f8956215f62186c8a9f2329", "guid": "bfdfe7dc352907fc980b868725387e982af67cb7b567ab7fa318a91c32ade8c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b72838c449dda971a35121d41fc43578", "guid": "bfdfe7dc352907fc980b868725387e988e3ec702f10fd46b525974c712062460"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98140e93e2d519c856570277af5e2950da", "guid": "bfdfe7dc352907fc980b868725387e98f812a7af6d7843cd1aafbed23a7cc2b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98061a7dc53fcc489c73df2ed6edce49ca", "guid": "bfdfe7dc352907fc980b868725387e98b05d9a098385ea3e71527283300ee4b9"}], "guid": "bfdfe7dc352907fc980b868725387e984828d07df21b03575280a435412b2bd2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9867aa7825f197b5c58a57320b4ccdc64e", "guid": "bfdfe7dc352907fc980b868725387e984dff9b9164ea7278ab55766135fcbf91"}], "guid": "bfdfe7dc352907fc980b868725387e98e02b84aa74e21e5d0824e135f776cc74", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9898f5fd0d63861b94769f137ac6982066", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e98f1ed40c0d488e7d17a2574221f5de571", "name": "just_audio", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f09d8f416b4b8830579237c0f3add196", "name": "just_audio.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}