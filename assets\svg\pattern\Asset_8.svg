<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 65.06 65.06">
  <defs>
    <style>
      .cls-1 {
        fill: #eee;
      }

      .cls-2 {
        isolation: isolate;
      }

      .cls-3 {
        mix-blend-mode: multiply;
        opacity: .5;
      }
    </style>
  </defs>
  <g class="cls-2">
    <g id="Layer_2" data-name="Layer 2">
      <g id="OBJECTS">
        <g class="cls-3">
          <path class="cls-1" d="M55.54,23v-13.48h-13.48L32.53,0l-9.53,9.53h-13.48v13.48L0,32.53l9.53,9.53v13.48h13.48l9.53,9.53,9.53-9.53h13.48v-13.48l9.53-9.53-9.53-9.53ZM50.74,40.08v10.67h-10.67l-7.54,7.54-7.54-7.54h-10.67v-10.67l-7.54-7.54,7.54-7.54v-10.67h10.67l7.54-7.55,7.54,7.55h10.67v10.67l7.54,7.54-7.54,7.54Z"/>
          <path class="cls-1" d="M47.47,26.34v-8.75h-8.75l-6.19-6.19-6.19,6.19h-8.75v8.75l-6.19,6.19,6.19,6.19v8.75h8.75l6.19,6.19,6.19-6.19h8.75v-8.75l6.19-6.19-6.19-6.19ZM44.39,37.99v6.95h-6.95l-4.91,4.91-4.91-4.91h-6.95v-6.95l-4.91-4.91,4.91-4.91v-6.95h6.95l4.91-4.91,4.91,4.91h6.95v6.95l4.91,4.91-4.91,4.91Z"/>
          <polygon class="cls-1" points="37.71 27.9 34.67 27.9 32.53 25.76 30.39 27.9 27.36 27.9 27.36 30.94 25.21 33.08 27.36 35.22 27.36 38.25 30.39 38.25 32.53 40.4 34.67 38.25 37.71 38.25 37.71 35.22 39.85 33.08 37.71 30.94 37.71 27.9"/>
        </g>
      </g>
    </g>
  </g>
</svg>