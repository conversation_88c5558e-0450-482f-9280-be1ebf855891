import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'dart:io' show Platform;
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:get/get.dart';
import 'package:gap/gap.dart';

import '/core/config/app_config.dart';
import '/core/utils/constants/extensions/extensions.dart';
import '/core/utils/constants/extensions/svg_extensions.dart';
import '/core/widgets/select_screen_build.dart';
import '/core/widgets/language_list.dart';
import '/core/widgets/mushaf_settings.dart';
import '/core/widgets/theme_change.dart';
import '/core/widgets/select_screen.dart';
import '/core/utils/constants/svg_constants.dart';
import '/core/utils/responsive_helper.dart';

import '/presentation/controllers/theme_controller.dart';
import '/presentation/controllers/general/general_controller.dart';
import '/presentation/screens/about_app/about_app.dart';
import '/presentation/screens/ourApp/screen/ourApps_screen.dart';

import '/features/admin/screens/admin_dashboard_screen.dart';
import '/features/settings/screens/content_management_screen.dart';

class MergedSettingsScreen extends HookConsumerWidget {
  const MergedSettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final generalCtrl = GeneralController.instance;
    final isMobile = ResponsiveHelper.isMobile(context);
    final isTablet = ResponsiveHelper.isTablet(context);
    final isDesktop = ResponsiveHelper.isDesktop(context);
    
    // Determine if the admin section should be shown
    bool showAdminSection = false;
    if (kIsWeb) {
      showAdminSection = true;
    } else {
      try {
        if (Platform.isWindows || Platform.isMacOS || Platform.isLinux) {
          showAdminSection = true;
        }
      } catch (e) {
        print('Error detecting platform: $e');
      }
    }

    return GetBuilder<ThemeController>(
      builder: (_) {
        return Scaffold(
          backgroundColor: Theme.of(context).colorScheme.primary,
          body: SafeArea(
            child: Container(
              color: Theme.of(context).colorScheme.primaryContainer,
              child: Obx(() => generalCtrl.state.showSelectScreenPage.value
                  ? const SelectScreenBuild(
                      isButtonBack: true,
                      isButton: false,
                    )
                  : Column(
                      children: [
                        _buildHeader(context),
                        Expanded(
                          child: _buildContent(context, showAdminSection),
                        ),
                      ],
                    ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          context.customClose(),
          const Gap(8),
          context.vDivider(height: 20),
          const Gap(8),
          Text(
            'settings'.tr,
            style: TextStyle(
              color: Theme.of(context).hintColor,
              fontFamily: 'kufi',
              fontWeight: FontWeight.bold,
              fontSize: 24,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context, bool showAdminSection) {
    final isDesktop = ResponsiveHelper.isDesktop(context);
    final isTablet = ResponsiveHelper.isTablet(context);
    
    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(
        horizontal: isDesktop ? 32.0 : (isTablet ? 24.0 : 16.0),
        vertical: 16.0,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Language Settings
          const LanguageList(),
          const Gap(24),
          
          // Quran Settings Section
          _buildSection(
            title: 'reading_settings'.tr,
            icon: Icons.menu_book,
            color: Colors.green,
            children: [
              MushafSettings(),
              const Gap(16),
              _buildSettingItem(
                title: 'font_size'.tr,
                subtitle: 'medium'.tr,
                icon: Icons.text_fields,
                onTap: () => _showFontSizeSettings(context),
              ),
              _buildSettingItem(
                title: 'show_translation'.tr,
                subtitle: 'enabled'.tr,
                icon: Icons.translate,
                onTap: () => _showTranslationSettings(context),
              ),
            ],
          ),
          const Gap(24),

          // Theme Settings
          const ThemeChange(),
          const Gap(24),

          // Screen Selection
          const SelectScreen(),
          const Gap(24),

          // AI Settings Section
          _buildSection(
            title: 'ai_settings'.tr,
            icon: Icons.psychology,
            color: Colors.purple,
            children: [
              _buildSettingItem(
                title: 'automatic_analysis'.tr,
                subtitle: 'enabled'.tr,
                icon: Icons.auto_fix_high,
                onTap: () => _showAISettings(context),
              ),
              _buildSettingItem(
                title: 'detail_level'.tr,
                subtitle: 'advanced'.tr,
                icon: Icons.tune,
                onTap: () => _showDetailLevelSettings(context),
              ),
            ],
          ),
          const Gap(24),

          // App Settings Section
          _buildSection(
            title: 'app_settings'.tr,
            icon: Icons.settings,
            color: Colors.blue,
            children: [
              _buildSettingItem(
                title: 'notifications'.tr,
                subtitle: 'enabled'.tr,
                icon: Icons.notifications,
                onTap: () => _showNotificationSettings(context),
              ),
              _buildSettingItem(
                title: 'backup'.tr,
                subtitle: 'automatic'.tr,
                icon: Icons.backup,
                onTap: () => _showBackupSettings(context),
              ),
              _buildSettingItem(
                title: 'Content Management',
                subtitle: 'Download tafsir databases',
                icon: Icons.download_rounded,
                onTap: () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const ContentManagementScreen(),
                  ),
                ),
              ),
            ],
          ),
          const Gap(24),

          // About Section
          _buildAboutSection(context),
          
          // Admin Section (conditionally displayed)
          if (showAdminSection) ...[
            const Gap(24),
            _buildSection(
              title: 'admin_panel'.tr,
              icon: Icons.admin_panel_settings,
              color: Colors.redAccent,
              children: [
                _buildSettingItem(
                  title: 'manage_ai_settings'.tr,
                  subtitle: 'control_models_prompts'.tr,
                  icon: Icons.model_training,
                  onTap: () => Get.to(() => const AdminDashboardScreen()),
                ),
              ],
            ),
          ],
          
          const Gap(32),
        ],
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required IconData icon,
    required Color color,
    required List<Widget> children,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(AppConfig.borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 28),
              const Gap(12),
              Text(
                title,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: color,
                  fontFamily: 'kufi',
                ),
              ),
            ],
          ),
          const Gap(16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildAboutSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(4.0),
      decoration: BoxDecoration(
        border: Border.all(
          color: Theme.of(context).colorScheme.surface,
          width: 1,
        ),
        borderRadius: const BorderRadius.all(Radius.circular(8)),
      ),
      child: Column(
        children: [
          _buildAboutItem(
            context: context,
            svgPath: SvgPath.svgAlheekmahLogo,
            title: 'ourApps'.tr,
            onTap: () => Get.to(() => const OurApps(), transition: Transition.downToUp),
          ),
          const Gap(8),
          _buildAboutItem(
            context: context,
            svgPath: SvgPath.svgSplashIcon,
            title: 'aboutApp'.tr,
            onTap: () => Get.to(() => const AboutApp(), transition: Transition.downToUp),
          ),
        ],
      ),
    );
  }

  Widget _buildAboutItem({
    required BuildContext context,
    required String svgPath,
    required String title,
    required VoidCallback onTap,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface.withValues(alpha: .2),
        borderRadius: const BorderRadius.all(Radius.circular(8)),
      ),
      child: InkWell(
        onTap: onTap,
        child: SizedBox(
          height: 50,
          child: Row(
            children: [
              Expanded(
                flex: 2,
                child: customSvgWithCustomColor(
                  svgPath,
                  height: 35,
                ),
              ),
              context.vDivider(height: 20.0),
              Expanded(
                flex: 8,
                child: Text(
                  title,
                  style: TextStyle(
                    fontFamily: 'kufi',
                    fontSize: 16,
                    color: Theme.of(context).hintColor,
                  ),
                ),
              ),
              const Spacer(),
              Icon(
                Icons.arrow_forward_ios_outlined,
                color: Theme.of(context).hintColor,
                size: 18,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSettingItem({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return Column(
      children: [
        ListTile(
          leading: Icon(icon, color: Colors.grey.shade600),
          title: Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              fontFamily: 'kufi',
            ),
          ),
          subtitle: Text(
            subtitle,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          trailing: const Icon(Icons.arrow_forward_ios, size: 16),
          onTap: onTap,
        ),
        const Divider(height: 1),
      ],
    );
  }

  void _showFontSizeSettings(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: Text('font_size'.tr),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('choose_font_size'.tr),
            const SizedBox(height: 16),
            ...['small'.tr, 'medium'.tr, 'large'.tr, 'very_large'.tr].map((size) =>
              RadioListTile<String>(
                title: Text(size),
                value: size,
                groupValue: 'medium'.tr,
                onChanged: (value) {
                  // TODO: Implement font size change
                },
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('cancel'.tr),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('save'.tr),
          ),
        ],
      ),
    );
  }

  void _showTranslationSettings(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: Text('translation_settings'.tr),
        content: Text('translation_settings_development'.tr),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('close'.tr),
          ),
        ],
      ),
    );
  }

  void _showAISettings(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: Text('ai_settings'.tr),
        content: Text('ai_settings_development'.tr),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('close'.tr),
          ),
        ],
      ),
    );
  }

  void _showDetailLevelSettings(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: Text('detail_level'.tr),
        content: Text('detail_level_settings_development'.tr),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('close'.tr),
          ),
        ],
      ),
    );
  }

  void _showNotificationSettings(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: Text('notification_settings'.tr),
        content: Text('notification_settings_development'.tr),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('close'.tr),
          ),
        ],
      ),
    );
  }

  void _showBackupSettings(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: Text('backup_settings'.tr),
        content: Text('backup_settings_development'.tr),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('close'.tr),
          ),
        ],
      ),
    );
  }
}