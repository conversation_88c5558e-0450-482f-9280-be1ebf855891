import 'dart:typed_data';
import 'package:flutter/foundation.dart' show kIsWeb;

// Conditional imports
import 'io_utils_native.dart' if (dart.library.html) 'io_utils_web.dart' as io_impl;

/// Cross-platform I/O utilities
abstract class IOUtils {
  static IOUtils? _instance;
  
  static IOUtils get instance {
    _instance ??= io_impl.createIOUtils();
    return _instance!;
  }
  
  /// Create a directory (no-op on web)
  Future<void> createDirectory(String path, {bool recursive = false});
  
  /// Check if a file exists
  Future<bool> fileExists(String path);
  
  /// Write bytes to a file
  Future<void> writeAsBytes(String path, List<int> bytes);
  
  /// Read bytes from a file
  Future<Uint8List?> readAsBytes(String path);
  
  /// Delete a file
  Future<void> deleteFile(String path);
  
  /// Get file length
  Future<int> getFileLength(String path);
  
  /// Check if directory exists
  Future<bool> directoryExists(String path);
  
  /// List directory contents
  Stream<String> listDirectory(String path);
  
  /// Rename a file
  Future<void> renameFile(String oldPath, String newPath);
}