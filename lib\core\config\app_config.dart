/// Application configuration constants and settings
class AppConfig {
  // App Information
  static const String appName = 'Quranic Insights';
  static const String appVersion = '1.0.0';
  
  // Design System
  static const double designWidth = 390.0;
  static const double designHeight = 844.0;
  
  // UI Constants
  static const double defaultPadding = 16.0;
  static const double cardElevation = 4.0;
  static const double borderRadius = 12.0;
  static const double iconSize = 24.0;
  static const double largePadding = 24.0;
  static const double smallPadding = 8.0;
  
  // Typography
  static const double headingFontSize = 24.0;
  static const double titleFontSize = 18.0;
  static const double bodyFontSize = 16.0;
  static const double captionFontSize = 12.0;
  static const double arabicFontSize = 18.0;
  static const double arabicLineHeight = 1.8;
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 150);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);
  
  // AI Configuration
  static const int maxRetryAttempts = 3;
  static const Duration aiRequestTimeout = Duration(seconds: 30);
  static const int maxRelatedVerses = 10;
  static const double minimumSimilarityThreshold = 0.5;
  
  // Cache Settings
  static const Duration cacheExpiration = Duration(hours: 2);
  static const int maxCacheEntries = 1000;
  
  // Localization
  static const String defaultLocale = 'ar';
  static const List<String> supportedLocales = <String>['ar', 'en'];
  
  // Database
  static const String databaseName = 'quranic_insights.db';
  static const int databaseVersion = 1;
  
  // Error Messages
  static const String genericErrorMessage = 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.';
  static const String networkErrorMessage = 'مشكلة في الاتصال بالإنترنت. يرجى التحقق من الاتصال.';
  static const String aiServiceErrorMessage = 'خطأ في خدمة الذكاء الاصطناعي. يرجى المحاولة لاحقاً.';
  static const String loadingMessage = 'جارٍ التحميل...';
  
  // Feature Flags
  static const bool enableAdvancedAnalytics = true;
  static const bool enableOfflineMode = false;
  static const bool enableExperimentalFeatures = false;
  
  // API Configuration
  static const int maxConcurrentRequests = 3;
  static const Duration rateLimitWindow = Duration(minutes: 1);
  static const int maxRequestsPerWindow = 60;
  
  // AI Service API Keys
  // Note: In production, these should be loaded from environment variables or secure storage
  // For client-side direct calls (e.g. embedding, or if ai-router is not used), 
  // ensure keys are provided via --dart-define during build.
  // Default values here are non-functional placeholders.
  static const String openaiApiKey = String.fromEnvironment(
    'OPENAI_API_KEY',
    defaultValue: 'YOUR_OPENAI_API_KEY_HERE_IF_NOT_SET_BY_DART_DEFINE', 
  );
  
  static const String claudeApiKey = String.fromEnvironment(
    'CLAUDE_API_KEY', 
    defaultValue: 'YOUR_CLAUDE_API_KEY_HERE_IF_NOT_SET_BY_DART_DEFINE',
  );
  
  // Helper methods to check if API keys are configured
  static bool get isOpenAIConfigured => 
      openaiApiKey != 'YOUR_OPENAI_API_KEY_HERE_IF_NOT_SET_BY_DART_DEFINE' && openaiApiKey.isNotEmpty;
  
  static bool get isClaudeConfigured => 
      claudeApiKey != 'YOUR_CLAUDE_API_KEY_HERE_IF_NOT_SET_BY_DART_DEFINE' && claudeApiKey.isNotEmpty;
      
  static bool get isAIConfigured => isOpenAIConfigured || isClaudeConfigured;
}
