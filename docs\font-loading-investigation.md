# Font Loading Investigation for Quranic Insights Web

## Issue Summary
Page-specific fonts (e.g., p2050.ttf for page 50) are not loading correctly on the web platform, despite being properly registered in pubspec.yaml and included in the web build.

## Investigation Findings

### 1. Font Registration
- Fonts are correctly registered in `pubspec.yaml` with family names like `page50`
- Font files exist at `assets/fonts/quran_fonts/p2050.ttf`
- FontManifest.json in the web build correctly includes all page fonts

### 2. Font Loading in Code
- The app uses `isFontsLocal: true` in `TextBuild` widget
- Font family name is set as `fontsName: 'page${pageIndex + 1}'` (e.g., 'page50')
- In `custom_span.dart`, when `isFontsLocal` is true, it uses the `fontsName` directly

### 3. Web Build Output
- Font files are correctly copied to `build/web/assets/assets/fonts/quran_fonts/`
- FontManifest.json lists all fonts with correct paths

## Potential Issues

### 1. Font Loading on Web
Flutter web might not automatically load all fonts declared in pubspec.yaml, especially when there are many fonts (604 page-specific fonts).

### 2. Renderer Issue
The web renderer (HTML vs CanvasKit) might affect font loading behavior.

### 3. Font Loading Timing
Fonts might not be loaded when the widget tries to use them, causing a fallback to default font.

## Solutions to Try

### Solution 1: Force HTML Renderer
Modified `web/index.html` to use HTML renderer:
```javascript
engineInitializer.initializeEngine({
  renderer: 'html'
}).then(function(appRunner) {
  appRunner.runApp();
});
```

### Solution 2: Explicit Font Loading (Future Implementation)
Create a WebFontLoader utility that explicitly loads fonts before use:
```dart
// This would ensure fonts are loaded on web before rendering
if (kIsWeb) {
  await FontLoader('page50').load();
}
```

### Solution 3: Use Default Quran Fonts
Instead of page-specific fonts, consider using a single Quranic font like 'hafs' or 'uthmanic2' which are already loaded.

### Solution 4: Lazy Load Fonts
Implement a system to load fonts only when needed, rather than declaring all 604 fonts upfront.

## Testing Steps

1. Build for web: `flutter build web --no-tree-shake-icons`
2. Serve the build: `cd build/web && python3 -m http.server 8000`
3. Open browser console and check for font loading errors
4. Use browser DevTools to inspect which font is actually being applied

## Next Steps

1. Test with HTML renderer to see if it improves font loading
2. Implement explicit font loading for web platform
3. Consider reducing the number of fonts loaded at once
4. Test with a subset of fonts to isolate the issue