import 'package:flutter/foundation.dart' show kIsWeb;
import 'dart:io' as io;

class PlatformCheck {
  static bool get isWeb => kIsWeb;
  
  static bool get isAndroid => !kIsWeb && io.Platform.isAndroid;
  static bool get isIOS => !kIsWeb && io.Platform.isIOS;
  static bool get isMacOS => !kIsWeb && io.Platform.isMacOS;
  static bool get isWindows => !kIsWeb && io.Platform.isWindows;
  static bool get isLinux => !kIsWeb && io.Platform.isLinux;
  static bool get isFuchsia => !kIsWeb && io.Platform.isFuchsia;
  
  static bool get isDesktop => isMacOS || isWindows || isLinux;
  static bool get isMobile => isAndroid || isIOS;
}