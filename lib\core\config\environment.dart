import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter_dotenv/flutter_dotenv.dart';

/// Environment configuration
/// 
/// Loads configuration from .env file for security
/// Create a .env file in the project root with your keys
class Environment {
  // For web builds, we need to use hardcoded values as .env files aren't available
  // In production, these should be injected during build time
  static const String _webSupabaseUrl = 'https://akhtiyivutfqwsjiltno.supabase.co';
  static const String _webSupabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFraHRpeWl2dXRmcXdzamlsdG5vIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDg5MDAsImV4cCI6MjA2NDI4NDkwMH0.hHlLNb31eh8VFzXAYK_Nvv1e_9uOnbbRrkIpjfdRGNE';
  
  static String get supabaseUrl => 
      kIsWeb ? _webSupabaseUrl : (dotenv.env['SUPABASE_URL'] ?? _webSupabaseUrl);
  
  static String get supabaseAnonKey => 
      kIsWeb ? _webSupabaseAnonKey : (dotenv.env['SUPABASE_ANON_KEY'] ?? '');
  
  static String get environment => 
      kIsWeb ? 'development' : (dotenv.env['ENVIRONMENT'] ?? 'development');
  
  static bool get isProduction => environment == 'production';
  static bool get isDevelopment => environment == 'development';
  
  /// Load environment variables from .env file
  static Future<void> load() async {
    if (!kIsWeb) {
      try {
        await dotenv.load(fileName: '.env');
      } catch (e) {
        print('Warning: Could not load .env file: $e');
      }
    }
  }
  
  /// Validate that required environment variables are set
  static bool validate() {
    if (supabaseAnonKey.isEmpty) {
      throw Exception('SUPABASE_ANON_KEY is not set');
    }
    return true;
  }
}