<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 114 114">
  <defs>
    <style>
      .cls-1 {
        mask: url(#mask);
      }

      .cls-2 {
        mix-blend-mode: multiply;
        opacity: .7;
      }

      .cls-3 {
        fill: #2f5690;
      }

      .cls-4 {
        fill: #e3a11d;
      }

      .cls-5 {
        isolation: isolate;
      }

      .cls-6 {
        fill: #fff;
        filter: url(#drop-shadow-1);
      }

      .cls-7 {
        fill: #a5a5a5;
      }

      .cls-8 {
        fill: #5f8b8e;
      }

      .cls-9 {
        filter: url(#luminosity-invert);
      }
    </style>
    <filter id="drop-shadow-1" x="0" y="0" width="114" height="114" filterUnits="userSpaceOnUse">
      <feOffset dx="5" dy="5"/>
      <feGaussianBlur result="blur" stdDeviation="8"/>
      <feFlood flood-color="#a5a5a5" flood-opacity=".75"/>
      <feComposite in2="blur" operator="in"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="luminosity-invert" x="23" y="24" width="57" height="56" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
      <feColorMatrix result="cm" values="-1 0 0 0 1 0 -1 0 0 1 0 0 -1 0 1 0 0 0 1 0"/>
    </filter>
    <mask id="mask" x="23" y="24" width="57" height="56" maskUnits="userSpaceOnUse">
      <g class="cls-9">
        <image width="57" height="56" transform="translate(23 24)" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADkAAAA4CAYAAABHRFAgAAAACXBIWXMAAAsSAAALEgHS3X78AAAI8ElEQVRogeWbeU/bzBbGnzhe4njLHhKKUFva7/95uqGqYokAO47jPbbfP7jn3HEIEBeSV7p3JKsCEs/8ZuY885xjtwWgwv94k/7tARyjycfqqN1uo9VqoaoqFEVxrG4BHAlSURRomgZJklCWJdI0RZ7nx+gawBEgFUVBt9utQUrSY5QcC/SgkASo6zpk+bGrsizR6XT4M8cAPRikCNhut1FVFV+tVguKoqAsS1RVhc1mc6hhADgQJAF2Oh3eolmWIcsyAI8iJEkSZFmGpmkAcFDQd4dUFAW6rkPTNLRaLRRFgTRNEUURQ5IQybIMRVH4u4cCbWEPMyBJEh8BrVZr943+8zdVVWsikyQJ4jh+Enu0igRZFAXyPEdVPT8c2u5FUaAsy70h91rJdrsNVVUhyzJDbg+GIElg8jxHnuc7AYH6qimKAlmWOXa3G92b4jdN0/eHpMGLCrk9GPqZVqMoCiRJ8gSGPiOClmVZm0DqUwSk77ZarcaKvDckdUoxRltnVyNI0dnYtg3HcQAAvu9jtVoxKK2QCCb2LUkST/RLIfMmSGrkVuI4btTJdDrFyckJTNMEAKzXa9ze3mKxWADAkwl5rum63qhfantDiudck3Z2dobz83OMx2MYhgEACMMQlmVBVVX8+fNnr/uUZdkoDsXW+AhpAvn161dcXFxgPp+j3++j2+0CAKIownA4hGVZ0HUd3759a9R304l+0zmpKAoMw2BvKl6WZWE+n2M2m2E0GsG2bT740zTFarXCYDDAcDjE6ekpgiBAkiR8hWGIMAz5bG0K9m6QhmFgOp1iMBjAcRxYlgXDMGCaJhzHwWAw4L8ZhlFT1zAMMRqNMJlM8OHDB6xWK75838f9/T1ubm7guu5bhrg/pHhOia3b7WIwGGA+n2M8HqPf78NxHFZS27Zh2/YTk77ZbBDHMQaDAYP6vg/P8+C6LguS7/s7IQ+irs/JtqZpcBwH4/EYs9kM4/GYV862bZimyYCqqnKKRe4mjmP0ej30ej0sl0uOUeBRnEQ1JdGje7wrpKIo7Ha2D2FN02CaJnq9HsbjMabTKUajEW9dMumKokBRlJpbyvMc3W6XY1r0u0mS4OHhoZaSicquKAo2m83eXvdFSNFsb3cEAKqqwjAMOI6Dfr+P4XCI4XDIMUhblHzv9r01TYOqqmzrACDLMgRBANu2a5BlWWKz2UCSJEiS1Ch7eRZyO6PP8/zJoa1pGgzDgGVZsCyLtyltO3H1ths5GDL/NOAkSeD7Pnq9HizL4s8XRYEsy/h7TbKXnRt8Vz6Y5zmSJKlBmqbJFymrGIP7CIQkSVBVFbquwzRNFisStC9fvtRgqD5UVRVnMrQL9obcldFTuiTO2KdPnzCdTnnGu90ur/xrne4C1TQNnU4H3W6Xt/9sNsPFxQXOzs5qoFmWsddVVRWdTufFPmt/2QYUvaooOvP5HB8/fmRIUlFVVWsq2qRROqfrOrrdLnq9HkajEebzOYIgQJZlfLTQZBMk7ZokSXZmKLLYCbkVWZZRVRWyLEMURbUv9vt9nJ6eYjqdot/vwzRN3p6KonB8NW2UTpHY6brOhmI8HiMIAsRxXMte0jSFLMsMScK4HaOS2AkpFwCGJFsFPGYBo9EIvV4Ptm1D13U+IkhEmh7U26DtdptXlVZWdFGi4JAYimJE4xAbr2RVVez0dyXFkiSh0+mw7FMSTWBvBdwF/FojqNc+y8iUDFM2X5Yl2u02zxzFZ5IkyLIMeZ5js9lwvaUoijeZaOqjKAreilmWIY5jhGGI9XoN3/c5dGRZ5mMKeFxVEqTtlKwmPGLsKYrCqkf7PIoiPDw8oN/vIwgCRFHE0CLw3wgP9UFHFcWf53m4u7vD7e0txyMBkkMiQBrHdnuiuwRKdZft2qjnebi+voZlWRgOh5hMJoiiiGe+KIrGRwhQ97NRFHEmcnV1hd+/f7OyEiCJDa36S89Xdo6GDlsqGW67i6urK6iqislkgpOTE4RhyKCkeE1UlrysuIKu6+Lm5gY/fvzg6oEI2G63OcRee4D07L4S3QUAhqXBX15e4vb2Fp7nYbVaMSht331LFSJgGIYIgoC36fX1Nb5//w7gv0ccjWFfQOAVg077m0r6sixz7AGPBSm6VqsVTNPkWQbAxuA59SO7KIqLmDiv12v+LI2BALMs2/sR4KvBQ2Kwq4KepikPzPd9GIZRS4w7nQ4fN6IYUcmSBhrHMXzfx3K5rAEmSVKDpL7JyO9bf91LIZ6r0qVpijAMeYCkxLT9xHySHvIQIHnQKIr4Hq7rwnVd+L6PMAyRpumTPmlymhSYG8uguJJZlmG9XsPzPHQ6HTbzVHIUi1y0mgRJKxhFEYIgQBAE8DwP9/f38DwPQRDshPybsmgjyO3Yotzv7u4OVVWxMlLqZds2DMPgIhaZfjEOgyCoxeJyucT9/T2Wy2UN8i1u6k3VujiOudAURRFc1+WShmVZ6PV6NRNPkEmSsINxXRfr9ZrVWfx9GIa1/v4WtPFKih1FUYS7uzus12v2tJTJGIaB2WyGyWTChWVSxjiO4XkeFosFFosF1us1ixD9G0URoih60v/fWMe9IQlQVMk8z+H7Pnzf3/mdz58/4/z8HJPJBI7j8BEUBAEWiwUuLy/x69evvfqn7ORvXo9pBCnWbOiwF2eWRIGM9s+fPxFFEVarFfr9PhRFQZ7nWC6XuL6+xtXVFQOIaR71JwKKpZiDQIqKRtbquWeUZLTJLN/c3CBNUwyHQ2iahjRN8fDwwLEs1mm2Y452D00CsPvZ6LtBUk3lpeCngWwf3K7rIo5jaJrGZyMBUrpELmk7ZRMfn1NZsulqNnpnYNuiiYOh31OKBtRzPLoPDXCX2aYd8NLDXQqFd3+c3uTZ4GazqWUv4oq+BJhlWSOr1qS9+ysuYtJKdZdWq8Vm+rlXYA75vt1BXlYSQakgJcsyJ9QEvk/C+x7tYK+dPVcbpe2b5/lRAIEDv0BIKdGud3ya5INvbQd/FZRASRkBcNHpWK+C7nWEvEcTX3Zq8mzxPdrRIAHUXMsx21Eh/632f/G/Cf4BF9uqOLBOFdMAAAAASUVORK5CYII="/>
      </g>
    </mask>
  </defs>
  <g class="cls-5">
    <g id="Layer_2" data-name="Layer 2">
      <g id="OBJECTS">
        <g>
          <path class="cls-6" d="M51.59,84.74l-9.53-9.53h-13.48v-13.48l-9.53-9.53,9.53-9.53v-13.48h13.48l9.53-9.53,9.53,9.53h13.48v13.48l9.53,9.53-9.53,9.53v13.48h-13.48l-9.53,9.53Z"/>
          <g>
            <g>
              <path class="cls-3" d="M51.59,77.96l-7.54-7.54h-10.67v-10.67l-7.54-7.54,7.54-7.54v-10.67h10.67l7.54-7.54,7.54,7.54h10.67v10.67l7.54,7.54-7.54,7.54v10.67h-10.67l-7.54,7.54ZM36.65,67.15h8.75l6.19,6.19,6.19-6.19h8.75v-8.75l6.19-6.19-6.19-6.19v-8.75h-8.75l-6.19-6.19-6.19,6.19h-8.75v8.75l-6.19,6.19,6.19,6.19v8.75Z"/>
              <polygon class="cls-4" points="63.45 57.67 68.36 52.75 63.45 47.84 63.45 40.89 56.5 40.89 51.59 35.98 46.68 40.89 39.73 40.89 39.73 47.84 34.82 52.75 39.73 57.67 39.73 64.61 46.68 64.61 51.59 69.52 56.5 64.61 63.45 64.61 63.45 57.67"/>
              <polygon class="cls-8" points="49.45 57.93 46.41 57.93 46.41 54.9 44.27 52.75 46.41 50.61 46.41 47.58 49.45 47.58 51.59 45.44 53.73 47.58 56.76 47.58 56.76 50.61 58.91 52.75 56.76 54.9 56.76 57.93 53.73 57.93 51.59 60.07 49.45 57.93"/>
            </g>
            <g class="cls-1">
              <g class="cls-2">
                <g>
                  <path class="cls-7" d="M51.59,77.96l-7.54-7.54h-10.67v-10.67l-7.54-7.54,7.54-7.54v-10.67h10.67l7.54-7.54,7.54,7.54h10.67v10.67l7.54,7.54-7.54,7.54v10.67h-10.67l-7.54,7.54ZM36.65,67.15h8.75l6.19,6.19,6.19-6.19h8.75v-8.75l6.19-6.19-6.19-6.19v-8.75h-8.75l-6.19-6.19-6.19,6.19h-8.75v8.75l-6.19,6.19,6.19,6.19v8.75Z"/>
                  <polygon class="cls-7" points="63.45 57.67 68.36 52.75 63.45 47.84 63.45 40.89 56.5 40.89 51.59 35.98 46.68 40.89 39.73 40.89 39.73 47.84 34.82 52.75 39.73 57.67 39.73 64.61 46.68 64.61 51.59 69.52 56.5 64.61 63.45 64.61 63.45 57.67"/>
                  <polygon class="cls-7" points="49.45 57.93 46.41 57.93 46.41 54.9 44.27 52.75 46.41 50.61 46.41 47.58 49.45 47.58 51.59 45.44 53.73 47.58 56.76 47.58 56.76 50.61 58.91 52.75 56.76 54.9 56.76 57.93 53.73 57.93 51.59 60.07 49.45 57.93"/>
                </g>
              </g>
            </g>
          </g>
        </g>
      </g>
    </g>
  </g>
</svg>