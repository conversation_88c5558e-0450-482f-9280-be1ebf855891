import 'dart:typed_data';
import 'package:get_storage/get_storage.dart';
import 'io_utils.dart';

IOUtils createIOUtils() => IOUtilsWeb();

class IOUtilsWeb implements IOUtils {
  static final GetStorage _storage = GetStorage('file_storage');
  
  @override
  Future<void> createDirectory(String path, {bool recursive = false}) async {
    // No-op on web - directories don't exist
  }
  
  @override
  Future<bool> fileExists(String path) async {
    await _storage.initStorage;
    return _storage.hasData(path);
  }
  
  @override
  Future<void> writeAsBytes(String path, List<int> bytes) async {
    await _storage.initStorage;
    await _storage.write(path, bytes);
  }
  
  @override
  Future<Uint8List?> readAsBytes(String path) async {
    await _storage.initStorage;
    final data = _storage.read<List<dynamic>>(path);
    if (data != null) {
      return Uint8List.fromList(data.cast<int>());
    }
    return null;
  }
  
  @override
  Future<void> deleteFile(String path) async {
    await _storage.initStorage;
    await _storage.remove(path);
  }
  
  @override
  Future<int> getFileLength(String path) async {
    await _storage.initStorage;
    final data = _storage.read<List<dynamic>>(path);
    return data?.length ?? 0;
  }
  
  @override
  Future<bool> directoryExists(String path) async {
    // Directories don't exist on web
    return true;
  }
  
  @override
  Stream<String> listDirectory(String path) async* {
    // On web, we can only list files we've stored
    await _storage.initStorage;
    final keys = _storage.getKeys<dynamic>();
    for (final key in keys) {
      if (key.toString().startsWith(path)) {
        yield key.toString();
      }
    }
  }
  
  @override
  Future<void> renameFile(String oldPath, String newPath) async {
    await _storage.initStorage;
    final data = _storage.read<List<dynamic>>(oldPath);
    if (data != null) {
      await _storage.write(newPath, data);
      await _storage.remove(oldPath);
    }
  }
}