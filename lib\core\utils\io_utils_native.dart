import 'dart:io';
import 'dart:typed_data';
import 'io_utils.dart';

IOUtils createIOUtils() => IOUtilsNative();

class IOUtilsNative implements IOUtils {
  @override
  Future<void> createDirectory(String path, {bool recursive = false}) async {
    final dir = Directory(path);
    if (!await dir.exists()) {
      await dir.create(recursive: recursive);
    }
  }
  
  @override
  Future<bool> fileExists(String path) async {
    return await File(path).exists();
  }
  
  @override
  Future<void> writeAsBytes(String path, List<int> bytes) async {
    await File(path).writeAsBytes(bytes);
  }
  
  @override
  Future<Uint8List?> readAsBytes(String path) async {
    final file = File(path);
    if (await file.exists()) {
      return await file.readAsBytes();
    }
    return null;
  }
  
  @override
  Future<void> deleteFile(String path) async {
    final file = File(path);
    if (await file.exists()) {
      await file.delete();
    }
  }
  
  @override
  Future<int> getFileLength(String path) async {
    final file = File(path);
    if (await file.exists()) {
      return await file.length();
    }
    return 0;
  }
  
  @override
  Future<bool> directoryExists(String path) async {
    return await Directory(path).exists();
  }
  
  @override
  Stream<String> listDirectory(String path) async* {
    final dir = Directory(path);
    if (await dir.exists()) {
      await for (final entity in dir.list()) {
        yield entity.path;
      }
    }
  }
  
  @override
  Future<void> renameFile(String oldPath, String newPath) async {
    await File(oldPath).rename(newPath);
  }
}