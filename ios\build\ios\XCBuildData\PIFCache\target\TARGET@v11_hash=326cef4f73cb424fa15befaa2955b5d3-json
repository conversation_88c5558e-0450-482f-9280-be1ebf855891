{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98207d0250060e83408c6567d99d1151fc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite_darwin/sqflite_darwin-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite_darwin", "PRODUCT_NAME": "sqflite_darwin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98693a079a09eb90f2409b9c32f74a8cdd", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98df4ea762b380bfae5609131771955c6c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite_darwin/sqflite_darwin-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite_darwin", "PRODUCT_NAME": "sqflite_darwin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b31145269069845c8cef21a5e541eb41", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98df4ea762b380bfae5609131771955c6c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite_darwin/sqflite_darwin-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite_darwin", "PRODUCT_NAME": "sqflite_darwin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983e77e3a8062a8bb73cddbceb5be79a21", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98755b319d7fb2a60600409a06b4c3795b", "guid": "bfdfe7dc352907fc980b868725387e9806e8b7c0ee268b24015d17d518f623c5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0ffa2767fed7ba12d16b52d5e1f325a", "guid": "bfdfe7dc352907fc980b868725387e980c279376498ad606dff1a7483df7e928"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f232b9803cd234c06ca00abf14d05296", "guid": "bfdfe7dc352907fc980b868725387e987df946a460f755998370fcb3569416e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c625aa2a75b87a8b8338ed64667be48d", "guid": "bfdfe7dc352907fc980b868725387e9868e8326e92c40ce39a7dcc24a40520a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e1e052c29a9c890852c719788e92470", "guid": "bfdfe7dc352907fc980b868725387e98732d5938c220684829243cca84b19827"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98671fb7cf78799813016e5bb1d71f673a", "guid": "bfdfe7dc352907fc980b868725387e98809b270bb247305a79842688815da8d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc6408377a64c01555accb20e773be13", "guid": "bfdfe7dc352907fc980b868725387e986a3827c3a079435e55224e11ed573d51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7049a5cd4d9e5d4993372ec2843d690", "guid": "bfdfe7dc352907fc980b868725387e98a1895ad1a04baa0197ea506a41bc7f8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c9229a3c6142785af4a28d23e6038e4", "guid": "bfdfe7dc352907fc980b868725387e98312791742db6c2d2413a8c33eba8bb80"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b7bf1662a1c0be198d3f6c0cd8a8b5a", "guid": "bfdfe7dc352907fc980b868725387e9869d8cdbee3cd7da6369ed61ac4ce98a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ca8ada56c95980c1cb5b1ba249640b7", "guid": "bfdfe7dc352907fc980b868725387e9891b7a91d3c7f7fbcdf1ecef2dd69e043", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef99bec315a2558ade73724226a4e606", "guid": "bfdfe7dc352907fc980b868725387e984c06b02526ff59f79582b4955fedf3d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7ed715a60afd8da824890ce7a6eb416", "guid": "bfdfe7dc352907fc980b868725387e9851ebff76b7845937f752de8b6bd58323"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ddb88e0686d49490239a60364086c2d", "guid": "bfdfe7dc352907fc980b868725387e98c101c6ec5cb468fac1b22b6955f016a6", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98021cea99905f4a0d1e6c3bd2991a0273", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984c759f8198f6b6083932e2dbd7db86ab", "guid": "bfdfe7dc352907fc980b868725387e98ed9c7433600d12926a713e8c9e0d83d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc2edf8e8571ca7f2c76a9cc4437a8ae", "guid": "bfdfe7dc352907fc980b868725387e989886952b20ac33fa589a1cd28b35bae6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed070f353d5bd986d6d813613140385f", "guid": "bfdfe7dc352907fc980b868725387e989c4fc507903b303c33b213c956447a08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891dedce9c2b51dcb60b26f5101456797", "guid": "bfdfe7dc352907fc980b868725387e98d8866dba5e9d28cce3f14273e66abc25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872c89ab67af336f43144ac4c93e19907", "guid": "bfdfe7dc352907fc980b868725387e989ed9fd6160e0a6b81ef2459731026bc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875cff21b1c015e59362b6eb24d0548e5", "guid": "bfdfe7dc352907fc980b868725387e98751dea23b0db1537e4872b152f84ffd1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ea8e666f6ab8168f093fb2be884af4d", "guid": "bfdfe7dc352907fc980b868725387e98aeb39828b3d8768fdd89bfd032d97b6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8042cc043c2d9257fe7833585507a2a", "guid": "bfdfe7dc352907fc980b868725387e98126bc9cf1bfaa63f111637054ad34f46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fae46e23eac5b9bfb6ad2b8fe9ea84eb", "guid": "bfdfe7dc352907fc980b868725387e98d21e0018ea670de4b3edadc1a77c5d71"}], "guid": "bfdfe7dc352907fc980b868725387e987b9a0a4516fd385618d3a8235a330cb4", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9867aa7825f197b5c58a57320b4ccdc64e", "guid": "bfdfe7dc352907fc980b868725387e9870affcdb149e2a2415f3aba76d90b801"}], "guid": "bfdfe7dc352907fc980b868725387e981b78713e6ca60fa025edc9c50c98ac4a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9836673e386db9aa601fc7093506284bb5", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9883134bb5f399cb37a1eb075d4fea30d8", "name": "sqflite_darwin-sqflite_darwin_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e981304d3d2169071b3ca365b19f5340b7c", "name": "sqflite_darwin", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98dbbec3eebed26c79cc653713be723aba", "name": "sqflite_darwin.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}