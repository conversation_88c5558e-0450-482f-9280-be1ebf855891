PODS:
  - audio_service (0.0.1):
    - Flutter
    - FlutterMac<PERSON>
  - audio_session (0.0.1):
    - Flutter
  - awesome_notifications (0.10.0):
    - Flutter
    - IosAwnCore (~> 0.10.0)
  - awesome_notifications_core (0.0.1):
    - Flutter
  - background_fetch (1.3.7):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - flutter_native_splash (2.4.3):
    - Flutter
  - flutter_timezone (0.0.1):
    - Flutter
  - IosAwnCore (0.10.0)
  - just_audio (0.0.1):
    - Flutter
    - FlutterMacOS
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - rate_my_app (2.3.0):
    - Flutter
    - FlutterMacOS
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - simple_animation_progress_bar (0.0.1):
    - Flutter
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - url_launcher_ios (0.0.1):
    - Flutter
  - wakelock_plus (0.0.1):
    - Flutter

DEPENDENCIES:
  - audio_service (from `.symlinks/plugins/audio_service/darwin`)
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - awesome_notifications (from `.symlinks/plugins/awesome_notifications/ios`)
  - awesome_notifications_core (from `.symlinks/plugins/awesome_notifications_core/ios`)
  - background_fetch (from `.symlinks/plugins/background_fetch/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - Flutter (from `Flutter`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - flutter_timezone (from `.symlinks/plugins/flutter_timezone/ios`)
  - just_audio (from `.symlinks/plugins/just_audio/darwin`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - rate_my_app (from `.symlinks/plugins/rate_my_app/darwin`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - simple_animation_progress_bar (from `.symlinks/plugins/simple_animation_progress_bar/ios`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)

SPEC REPOS:
  trunk:
    - IosAwnCore

EXTERNAL SOURCES:
  audio_service:
    :path: ".symlinks/plugins/audio_service/darwin"
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  awesome_notifications:
    :path: ".symlinks/plugins/awesome_notifications/ios"
  awesome_notifications_core:
    :path: ".symlinks/plugins/awesome_notifications_core/ios"
  background_fetch:
    :path: ".symlinks/plugins/background_fetch/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  Flutter:
    :path: Flutter
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  flutter_timezone:
    :path: ".symlinks/plugins/flutter_timezone/ios"
  just_audio:
    :path: ".symlinks/plugins/just_audio/darwin"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  rate_my_app:
    :path: ".symlinks/plugins/rate_my_app/darwin"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  simple_animation_progress_bar:
    :path: ".symlinks/plugins/simple_animation_progress_bar/ios"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"

SPEC CHECKSUMS:
  audio_service: cab6c1a0eaf01b5a35b567e11fa67d3cc1956910
  audio_session: 19e9480dbdd4e5f6c4543826b2e8b0e4ab6145fe
  awesome_notifications: dd5518ff1c80be03d4f1c40f04da9d9cc2a37af5
  awesome_notifications_core: d02eed89738fa362d56cbd372850e9adcd2c6bef
  background_fetch: 39f11371c0dce04b001c4bfd5e782bcccb0a85e2
  connectivity_plus: 2a701ffec2c0ae28a48cf7540e279787e77c447d
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_native_splash: f71420956eb811e6d310720fee915f1d42852e7a
  flutter_timezone: ac3da59ac941ff1c98a2e1f0293420e020120282
  IosAwnCore: 653786a911089012092ce831f2945cd339855a89
  just_audio: a42c63806f16995daf5b219ae1d679deb76e6a79
  package_info_plus: c0502532a26c7662a62a356cebe2692ec5fe4ec4
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  rate_my_app: 79b255442c34c58affc733ccf39bb37fb74a198f
  share_plus: 8b6f8b3447e494cca5317c8c3073de39b3600d1f
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  simple_animation_progress_bar: 3f818acdbd78de0a8c48ddd7fdc88e1998a7b33f
  sqflite_darwin: 5a7236e3b501866c1c9befc6771dfd73ffb8702d
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe
  wakelock_plus: 373cfe59b235a6dd5837d0fb88791d2f13a90d56

PODFILE CHECKSUM: d4c1a5030a8cbce3d2276496379558613c182943

COCOAPODS: 1.16.2
