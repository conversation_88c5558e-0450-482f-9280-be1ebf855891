<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 168 168">
  <defs>
    <style>
      .cls-1 {
        fill: #0c2c4c;
      }

      .cls-2 {
        fill: #2f5690;
      }

      .cls-3 {
        filter: url(#drop-shadow-3);
      }

      .cls-3, .cls-4, .cls-5 {
        fill: #fff;
      }

      .cls-6 {
        fill: #e3a11d;
      }

      .cls-7 {
        opacity: .8;
      }

      .cls-4 {
        filter: url(#drop-shadow-1);
      }

      .cls-5 {
        filter: url(#drop-shadow-2);
      }

      .cls-8 {
        fill: #5f8b8e;
      }

      .cls-9 {
        fill: #b75725;
      }
    </style>
    <filter id="drop-shadow-1" x="0" y="0" width="168" height="168" filterUnits="userSpaceOnUse">
      <feOffset dx="5" dy="5"/>
      <feGaussianBlur result="blur" stdDeviation="8"/>
      <feFlood flood-color="#a5a5a5" flood-opacity=".75"/>
      <feComposite in2="blur" operator="in"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="drop-shadow-2" x="11" y="11" width="138" height="138" filterUnits="userSpaceOnUse">
      <feOffset dx="1" dy="1"/>
      <feGaussianBlur result="blur-2" stdDeviation="3"/>
      <feFlood flood-color="#a5a5a5" flood-opacity="1"/>
      <feComposite in2="blur-2" operator="in"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="drop-shadow-3" x="58" y="59" width="43" height="43" filterUnits="userSpaceOnUse">
      <feOffset dx="1" dy="1"/>
      <feGaussianBlur result="blur-3" stdDeviation="3"/>
      <feFlood flood-color="#a5a5a5" flood-opacity="1"/>
      <feComposite in2="blur-3" operator="in"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="OBJECTS">
    <g>
      <g>
        <polygon class="cls-4" points="120.86 96.45 138.27 79.05 120.86 61.64 120.86 37.02 96.24 37.02 78.83 19.61 61.42 37.02 36.8 37.02 36.8 61.64 19.39 79.05 36.8 96.45 36.8 121.07 61.42 121.07 78.83 138.48 96.24 121.07 120.86 121.07 120.86 96.45"/>
        <g>
          <polygon class="cls-1" points="83.34 74.54 80.7 74.54 78.83 72.67 76.96 74.54 74.32 74.54 74.32 77.18 72.45 79.05 74.32 80.91 74.32 83.56 76.96 83.56 78.83 85.42 80.7 83.56 83.34 83.56 83.34 80.91 85.21 79.05 83.34 77.18 83.34 74.54"/>
          <polygon class="cls-9" points="65.77 52.75 66.62 63 70.02 63 65.77 52.75"/>
          <polygon class="cls-9" points="88.19 51.22 81.54 59.07 83.94 61.47 88.19 51.22"/>
          <path class="cls-8" d="M84.64,67h-.82l-.59-.59s0,0,0,0l-4.39-4.39-4.39,4.39h0s-.59.6-.59.6h-7.06v6.21h0v.85l-.59.59s0,0,0,0l-4.39,4.39,4.99,4.99v.84h0v6.22h7.06l.59.59s0,0,0,0l4.39,4.39,4.39-4.39s0,0,.01-.01l.59-.59h7.06v-6.21h0v-.85l.59-.59s0,0,0,0l4.39-4.39-4.39-4.39s0,0,0,0l-.59-.59v-.84h0v-6.22h-6.22s0,0,0,0c0,0,0,0,0,0ZM90.86,79.05l-3.52,3.52v4.99h-4.99l-3.53,3.53-3.53-3.53h-4.99v-4.98l-3.53-3.53,3.53-3.53v-4.99h4.99l3.53-3.53,3.53,3.53h4.99v4.99l3.52,3.53Z"/>
          <polygon class="cls-6" points="69.47 51.22 73.71 61.47 76.11 59.07 69.47 51.22"/>
          <polygon class="cls-9" points="40.04 65.65 26.64 79.05 40.04 92.44 55.87 79.05 40.04 65.65"/>
          <polygon class="cls-2" points="49.48 42.14 41.93 42.14 41.93 49.7 50.17 50.38 49.48 42.14"/>
          <polygon class="cls-8" points="60.87 42.14 53.5 42.14 54.55 54.76 41.93 53.71 41.93 61.09 62.59 62.81 60.87 42.14"/>
          <polygon class="cls-2" points="115.73 49.7 115.73 42.14 108.18 42.14 107.49 50.38 115.73 49.7"/>
          <polygon class="cls-6" points="92.23 40.26 78.83 26.86 65.43 40.26 78.83 56.09 92.23 40.26"/>
          <polygon class="cls-9" points="94.87 70.23 105.13 65.99 94.87 66.84 94.87 70.23"/>
          <polygon class="cls-6" points="91.89 52.75 87.64 63 91.03 63 91.89 52.75"/>
          <polygon class="cls-6" points="105.13 92.1 94.87 87.86 94.87 91.25 105.13 92.1"/>
          <polygon class="cls-6" points="88.19 106.87 83.94 96.62 81.54 99.02 88.19 106.87"/>
          <polygon class="cls-9" points="91.89 105.34 91.03 95.09 87.64 95.09 91.89 105.34"/>
          <polygon class="cls-9" points="106.66 88.41 98.81 81.76 96.4 84.16 106.66 88.41"/>
          <polygon class="cls-6" points="52.53 65.99 62.78 70.23 62.78 66.84 52.53 65.99"/>
          <polygon class="cls-6" points="106.66 69.68 96.41 73.93 98.81 76.33 106.66 69.68"/>
          <polygon class="cls-9" points="51 69.68 58.85 76.33 61.25 73.93 51 69.68"/>
          <polygon class="cls-6" points="51 88.41 61.25 84.16 58.85 81.76 51 88.41"/>
          <polygon class="cls-6" points="65.77 105.34 70.02 95.09 66.62 95.09 65.77 105.34"/>
          <polygon class="cls-9" points="69.47 106.88 76.11 99.02 73.71 96.62 69.47 106.88"/>
          <polygon class="cls-9" points="52.53 92.1 62.78 91.25 62.78 87.86 52.53 92.1"/>
          <polygon class="cls-8" points="115.73 61.09 115.73 53.71 103.11 54.76 104.16 42.14 96.78 42.14 95.06 62.81 115.73 61.09"/>
          <polygon class="cls-8" points="41.93 97 41.93 104.38 54.55 103.33 53.5 115.95 60.87 115.95 62.59 95.28 41.93 97"/>
          <polygon class="cls-6" points="65.43 117.83 78.83 131.23 92.23 117.83 78.83 102 65.43 117.83"/>
          <polygon class="cls-2" points="41.93 108.39 41.93 115.95 49.48 115.95 50.17 107.71 41.93 108.39"/>
          <polygon class="cls-9" points="117.62 65.65 101.79 79.05 117.62 92.44 131.02 79.05 117.62 65.65"/>
          <polygon class="cls-8" points="96.78 115.95 104.16 115.95 103.11 103.33 115.73 104.38 115.73 97 95.06 95.28 96.78 115.95"/>
          <polygon class="cls-2" points="108.18 115.95 115.73 115.95 115.73 108.39 107.49 107.71 108.18 115.95"/>
        </g>
      </g>
      <g class="cls-7">
        <g>
          <path class="cls-5" d="M120.86,61.64v-24.62h-24.62l-17.41-17.41-17.41,17.41h-24.62v24.62l-17.41,17.41,17.41,17.41v24.62h24.62l17.41,17.41,17.41-17.41h24.62v-24.62l17.41-17.41-17.41-17.41ZM108.18,42.14h7.55v7.55l-8.24.68.69-8.24ZM96.78,42.14h7.38l-1.05,12.62,12.62-1.05v7.38l-20.67,1.72,1.72-20.67ZM96.4,73.93l10.25-4.25-7.85,6.65-2.4-2.4ZM106.66,88.41l-10.25-4.25,2.4-2.4,7.85,6.65ZM94.87,66.84l10.25-.85-10.25,4.25v-3.39ZM87.64,95.09h3.39l.85,10.25-4.25-10.25ZM88.19,106.87l-6.65-7.85,2.4-2.4,4.25,10.25ZM91.47,83.44s0,0,0,0l-.59.59v.84h0v6.22h-7.06l-.59.59s0,0-.01.01l-4.39,4.39-4.39-4.39s0,0,0,0l-.59-.59h-7.06v-6.21h0v-.85l-4.99-4.99,4.39-4.39s0,0,0,0l.59-.59v-.84h0v-6.22h7.06l.59-.59s0,0,0,0l4.39-4.39,4.39,4.39s0,0,0,0l.59.59h.82s0,0,0,0c0,0,0,0,0,0h6.22v6.21h0v.85l.59.59s0,0,0,0l4.39,4.39-4.39,4.39ZM73.71,96.62l2.4,2.4-6.65,7.85,4.25-10.25ZM65.77,105.34l.85-10.25h3.39l-4.25,10.25ZM70.02,63h-3.39l-.85-10.25,4.25,10.25ZM69.47,51.22l6.65,7.85-2.4,2.4-4.25-10.25ZM83.94,61.47l-2.4-2.4,6.65-7.85-4.25,10.25ZM91.89,52.75l-.85,10.25h-3.39l4.25-10.25ZM78.83,26.86l13.4,13.4-13.4,15.83-13.4-15.83,13.4-13.4ZM62.78,70.23l-10.25-4.25,10.25.85v3.39ZM61.25,84.16l-10.25,4.25,7.85-6.65,2.4,2.4ZM51,69.68l10.25,4.25-2.4,2.4-7.85-6.65ZM41.93,42.14h7.55l.69,8.24-8.24-.68v-7.55ZM41.93,53.71l12.62,1.05-1.05-12.62h7.38l1.72,20.67-20.67-1.72v-7.38ZM26.64,79.05l13.4-13.4,15.83,13.4-15.83,13.4-13.4-13.4ZM49.48,115.95h-7.55v-7.55l8.24-.69-.69,8.24ZM60.87,115.95h-7.38l1.05-12.62-12.62,1.05v-7.38l20.67-1.72-1.72,20.67ZM62.78,91.25l-10.25.85,10.25-4.25v3.39ZM78.83,131.23l-13.4-13.4,13.4-15.83,13.4,15.83-13.4,13.4ZM94.87,87.86l10.25,4.25-10.25-.85v-3.39ZM115.73,115.95h-7.55l-.69-8.24,8.24.69v7.55ZM115.73,104.38l-12.62-1.05,1.05,12.62h-7.38l-1.72-20.67,20.67,1.72v7.38ZM117.62,92.44l-15.83-13.4,15.83-13.4,13.4,13.4-13.4,13.4Z"/>
          <path class="cls-3" d="M87.34,70.54h-4.99l-3.53-3.53-3.53,3.53h-4.99v4.99l-3.53,3.53,3.53,3.53v4.98h4.99l3.53,3.53,3.53-3.53h4.99v-4.99l3.52-3.52-3.52-3.53v-4.99ZM83.34,80.91v2.64h-2.64l-1.87,1.87-1.87-1.87h-2.64v-2.64l-1.87-1.87,1.87-1.87v-2.64h2.64l1.87-1.87,1.87,1.87h2.64v2.64l1.87,1.87-1.87,1.87Z"/>
        </g>
      </g>
    </g>
  </g>
</svg>