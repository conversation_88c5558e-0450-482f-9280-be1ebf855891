import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '/core/utils/constants/svg_constants.dart';
import '/core/utils/responsive_helper.dart';

/// Main layout for web and desktop platforms with persistent sidebar
class WebDesktopLayout extends StatefulWidget {
  final Widget child;
  final int selectedIndex;
  final Function(int)? onDestinationSelected;

  const WebDesktopLayout({
    super.key,
    required this.child,
    this.selectedIndex = 0,
    this.onDestinationSelected,
  });

  @override
  State<WebDesktopLayout> createState() => _WebDesktopLayoutState();
}

class _WebDesktopLayoutState extends State<WebDesktopLayout> {
  bool _isSidebarExpanded = true;
  final _shortcuts = <ShortcutActivator, Intent>{
    const SingleActivator(LogicalKeyboardKey.digit1, control: true):
        NavigateIntent(0),
    const SingleActivator(LogicalKeyboardKey.digit2, control: true):
        NavigateIntent(1),
    const SingleActivator(LogicalKeyboardKey.digit3, control: true):
        NavigateIntent(2),
    const SingleActivator(LogicalKeyboardKey.digit4, control: true):
        NavigateIntent(3),
    const SingleActivator(LogicalKeyboardKey.keyB, control: true):
        ToggleSidebarIntent(),
  };

  @override
  Widget build(BuildContext context) {
    final isLargeDesktop = ResponsiveHelper.isLargeDesktop(context);
    final sidebarWidth = _isSidebarExpanded ? (isLargeDesktop ? 280.0 : 240.0) : 72.0;

    return Shortcuts(
      shortcuts: _shortcuts,
      child: Actions(
        actions: {
          NavigateIntent: CallbackAction<NavigateIntent>(
            onInvoke: (intent) {
              widget.onDestinationSelected?.call(intent.index);
              return null;
            },
          ),
          ToggleSidebarIntent: CallbackAction<ToggleSidebarIntent>(
            onInvoke: (_) {
              setState(() => _isSidebarExpanded = !_isSidebarExpanded);
              return null;
            },
          ),
        },
        child: Focus(
          autofocus: true,
          child: Scaffold(
            body: Row(
              children: [
                // Sidebar
                AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  width: sidebarWidth,
                  child: _buildSidebar(context),
                ),
                // Main content area
                Expanded(
                  child: Container(
                    color: Theme.of(context).colorScheme.surface,
                    child: widget.child,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSidebar(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainer,
        border: Border(
          right: BorderSide(
            color: colorScheme.outlineVariant,
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          // Logo and app name
          Container(
            height: 80,
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                SvgPicture.asset(
                  SvgPath.svgSplashIcon,
                  height: 48,
                  colorFilter: ColorFilter.mode(
                    colorScheme.primary,
                    BlendMode.srcIn,
                  ),
                ),
                if (_isSidebarExpanded) ...[
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'appName'.tr,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'aiPowered'.tr,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
          const Divider(height: 1),
          // Navigation items
          Expanded(
            child: ListView(
              padding: const EdgeInsets.symmetric(vertical: 8),
              children: [
                _buildNavItem(
                  context,
                  icon: Icons.home_outlined,
                  selectedIcon: Icons.home,
                  label: 'home'.tr,
                  index: 0,
                  shortcut: 'Ctrl+1',
                ),
                _buildNavItem(
                  context,
                  icon: Icons.menu_book_outlined,
                  selectedIcon: Icons.menu_book,
                  label: 'quran'.tr,
                  index: 1,
                  shortcut: 'Ctrl+2',
                ),
                _buildNavItem(
                  context,
                  icon: Icons.psychology_outlined,
                  selectedIcon: Icons.psychology,
                  label: 'aiInsights'.tr,
                  index: 2,
                  shortcut: 'Ctrl+3',
                ),
                _buildNavItem(
                  context,
                  icon: Icons.bookmarks_outlined,
                  selectedIcon: Icons.bookmarks,
                  label: 'bookmarks'.tr,
                  index: 3,
                  shortcut: 'Ctrl+4',
                ),
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Divider(),
                ),
                _buildNavItem(
                  context,
                  icon: Icons.favorite_outline,
                  selectedIcon: Icons.favorite,
                  label: 'adhkar'.tr,
                  index: 4,
                ),
                _buildNavItem(
                  context,
                  icon: Icons.library_books_outlined,
                  selectedIcon: Icons.library_books,
                  label: 'tafsir'.tr,
                  index: 5,
                ),
                _buildNavItem(
                  context,
                  icon: Icons.school_outlined,
                  selectedIcon: Icons.school,
                  label: 'scholars'.tr,
                  index: 6,
                ),
              ],
            ),
          ),
          // Bottom actions
          const Divider(height: 1),
          _buildNavItem(
            context,
            icon: Icons.settings_outlined,
            selectedIcon: Icons.settings,
            label: 'settings'.tr,
            index: 7,
          ),
          // Sidebar toggle
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: IconButton(
              onPressed: () => setState(() => _isSidebarExpanded = !_isSidebarExpanded),
              icon: Icon(_isSidebarExpanded
                  ? Icons.keyboard_double_arrow_left
                  : Icons.keyboard_double_arrow_right),
              tooltip: _isSidebarExpanded ? 'collapseSidebar'.tr : 'expandSidebar'.tr,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem(
    BuildContext context, {
    required IconData icon,
    required IconData selectedIcon,
    required String label,
    required int index,
    String? shortcut,
  }) {
    final isSelected = widget.selectedIndex == index;
    final colorScheme = Theme.of(context).colorScheme;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => widget.onDestinationSelected?.call(index),
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: 16,
              vertical: _isSidebarExpanded ? 12 : 16,
            ),
            decoration: BoxDecoration(
              color: isSelected
                  ? colorScheme.primaryContainer.withValues(alpha: 0.8)
                  : null,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Icon(
                  isSelected ? selectedIcon : icon,
                  color: isSelected
                      ? colorScheme.onPrimaryContainer
                      : colorScheme.onSurfaceVariant,
                ),
                if (_isSidebarExpanded) ...[
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      label,
                      style: TextStyle(
                        color: isSelected
                            ? colorScheme.onPrimaryContainer
                            : colorScheme.onSurfaceVariant,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                      ),
                    ),
                  ),
                  if (shortcut != null)
                    Text(
                      shortcut,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: colorScheme.onSurfaceVariant.withValues(alpha: 0.5),
                          ),
                    ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// Intent classes for keyboard shortcuts
class NavigateIntent extends Intent {
  final int index;
  const NavigateIntent(this.index);
}

class ToggleSidebarIntent extends Intent {
  const ToggleSidebarIntent();
}