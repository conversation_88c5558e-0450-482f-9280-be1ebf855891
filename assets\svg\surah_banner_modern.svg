<svg width="373" height="50" viewBox="0 0 373 50" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Definitions -->
  <defs>
    <!-- Modern gradient -->
    <linearGradient id="modernGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#2E7D32;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1B5E20;stop-opacity:1" />
    </linearGradient>
    
    <!-- Subtle pattern -->
    <pattern id="dotPattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
      <circle cx="10" cy="10" r="1" fill="#ffffff" opacity="0.15"/>
    </pattern>
    
    <!-- Glow effect -->
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Main banner shape with modern cut corners -->
  <path d="M50 5 L323 5 Q333 5 333 15 L333 35 Q333 45 323 45 L50 45 Q40 45 40 35 L40 15 Q40 5 50 5 Z" 
        fill="url(#modernGradient)"/>
  
  <!-- Pattern overlay -->
  <path d="M50 5 L323 5 Q333 5 333 15 L333 35 Q333 45 323 45 L50 45 Q40 45 40 35 L40 15 Q40 5 50 5 Z" 
        fill="url(#dotPattern)"/>
  
  <!-- Left wing design -->
  <g transform="translate(0, 25)">
    <!-- Geometric wing -->
    <path d="M40 0 L25 -15 Q20 -17 18 -12 L25 -5 L18 -5 Q10 -5 8 0 Q10 5 18 5 L25 5 L18 12 Q20 17 25 15 L40 0 Z" 
          fill="#1B5E20" stroke="#FFD700" stroke-width="0.5" filter="url(#glow)"/>
    <!-- Inner details -->
    <circle cx="30" cy="0" r="3" fill="#FFD700" opacity="0.7"/>
    <circle cx="30" cy="0" r="1.5" fill="#ffffff"/>
  </g>
  
  <!-- Right wing design (mirrored) -->
  <g transform="translate(373, 25) scale(-1, 1)">
    <!-- Geometric wing -->
    <path d="M40 0 L25 -15 Q20 -17 18 -12 L25 -5 L18 -5 Q10 -5 8 0 Q10 5 18 5 L25 5 L18 12 Q20 17 25 15 L40 0 Z" 
          fill="#1B5E20" stroke="#FFD700" stroke-width="0.5" filter="url(#glow)"/>
    <!-- Inner details -->
    <circle cx="30" cy="0" r="3" fill="#FFD700" opacity="0.7"/>
    <circle cx="30" cy="0" r="1.5" fill="#ffffff"/>
  </g>
  
  <!-- Top accent line -->
  <rect x="60" y="3" width="253" height="1" fill="#FFD700" opacity="0.6"/>
  
  <!-- Bottom accent line -->
  <rect x="60" y="46" width="253" height="1" fill="#FFD700" opacity="0.6"/>
  
  <!-- Corner accents -->
  <!-- Top left -->
  <g transform="translate(45, 10)">
    <path d="M0 0 L10 0 L0 10 Z" fill="none" stroke="#FFD700" stroke-width="0.5" opacity="0.5"/>
    <circle cx="2" cy="2" r="1" fill="#FFD700" opacity="0.7"/>
  </g>
  
  <!-- Top right -->
  <g transform="translate(328, 10) scale(-1, 1)">
    <path d="M0 0 L10 0 L0 10 Z" fill="none" stroke="#FFD700" stroke-width="0.5" opacity="0.5"/>
    <circle cx="2" cy="2" r="1" fill="#FFD700" opacity="0.7"/>
  </g>
  
  <!-- Bottom left -->
  <g transform="translate(45, 40) scale(1, -1)">
    <path d="M0 0 L10 0 L0 10 Z" fill="none" stroke="#FFD700" stroke-width="0.5" opacity="0.5"/>
    <circle cx="2" cy="2" r="1" fill="#FFD700" opacity="0.7"/>
  </g>
  
  <!-- Bottom right -->
  <g transform="translate(328, 40) scale(-1, -1)">
    <path d="M0 0 L10 0 L0 10 Z" fill="none" stroke="#FFD700" stroke-width="0.5" opacity="0.5"/>
    <circle cx="2" cy="2" r="1" fill="#FFD700" opacity="0.7"/>
  </g>
  
  <!-- Center decoration -->
  <g transform="translate(186.5, 25)">
    <!-- Outer ring -->
    <circle cx="0" cy="0" r="18" fill="none" stroke="#ffffff" stroke-width="0.5" opacity="0.3"/>
    <!-- Middle ring -->
    <circle cx="0" cy="0" r="15" fill="none" stroke="#FFD700" stroke-width="0.5" opacity="0.5"/>
    <!-- Center placeholder for surah name -->
    <g id="surah-name-container">
      <!-- Surah name SVG will be placed here -->
    </g>
  </g>
  
  <!-- Side ornaments -->
  <!-- Left -->
  <g transform="translate(70, 25)">
    <path d="M-5 0 L0 -5 L5 0 L0 5 Z" fill="#FFD700" opacity="0.3"/>
    <circle cx="0" cy="0" r="1" fill="#ffffff" opacity="0.5"/>
  </g>
  
  <!-- Right -->
  <g transform="translate(303, 25)">
    <path d="M-5 0 L0 -5 L5 0 L0 5 Z" fill="#FFD700" opacity="0.3"/>
    <circle cx="0" cy="0" r="1" fill="#ffffff" opacity="0.5"/>
  </g>
</svg>